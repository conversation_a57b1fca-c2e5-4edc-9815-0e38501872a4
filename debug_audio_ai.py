import speech_recognition as sr
import google.generativeai as genai
import time
from datetime import datetime

class DebugAudioAI:
    def __init__(self, api_key):
        print("🔧 DEBUG AUDIO AI - Starting...")
        
        # Configure AI
        try:
            genai.configure(api_key=api_key)
            self.model = genai.GenerativeModel('gemini-1.5-flash')
            print("✅ AI configured successfully")
        except Exception as e:
            print(f"❌ AI configuration failed: {e}")
            return
        
        # Setup speech recognition
        self.recognizer = sr.Recognizer()
        
        # Configure recognizer for better detection
        self.recognizer.energy_threshold = 300  # Adjust sensitivity
        self.recognizer.dynamic_energy_threshold = True
        self.recognizer.pause_threshold = 0.8  # Seconds of silence before considering phrase complete
        
        print("✅ Speech recognizer configured")
        
        # Test microphone
        self.test_microphone()
        
        self.is_running = False
    
    def test_microphone(self):
        """Test microphone availability"""
        print("🎤 Testing microphone...")
        
        try:
            # List available microphones
            mic_list = sr.Microphone.list_microphone_names()
            print(f"📱 Found {len(mic_list)} microphones:")
            for i, name in enumerate(mic_list[:3]):
                print(f"   {i}: {name}")
            
            # Test default microphone
            self.microphone = sr.Microphone()
            with self.microphone as source:
                print("🔊 Adjusting for ambient noise...")
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
                print(f"🎚️ Energy threshold set to: {self.recognizer.energy_threshold}")
            
            print("✅ Microphone test successful")
            
        except Exception as e:
            print(f"❌ Microphone test failed: {e}")
    
    def listen_once(self):
        """Listen for audio once with detailed logging"""
        try:
            print(f"\n🎧 [{datetime.now().strftime('%H:%M:%S')}] Listening...")
            
            with self.microphone as source:
                # Listen with timeout
                audio = self.recognizer.listen(source, timeout=2, phrase_time_limit=3)
                print("📊 Audio captured, processing...")
                
                # Try to recognize
                try:
                    text = self.recognizer.recognize_google(audio, language='en-IN')
                    print(f"✅ RECOGNIZED: {text}")
                    return text
                except sr.UnknownValueError:
                    print("❓ Audio unclear")
                    return None
                except sr.RequestError as e:
                    print(f"❌ Recognition service error: {e}")
                    return None
                    
        except sr.WaitTimeoutError:
            print("⏰ No audio detected (timeout)")
            return None
        except Exception as e:
            print(f"❌ Listen error: {e}")
            return None
    
    def get_ai_response(self, text):
        """Get AI response with error handling"""
        try:
            print("🤖 Getting AI response...")
            prompt = f"Someone said: '{text}'\nGive a brief helpful response in English:"
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            print(f"❌ AI error: {e}")
            return "Sorry, AI is not responding"
    
    def run_continuous(self):
        """Run continuous listening"""
        self.is_running = True
        
        print("\n" + "="*60)
        print("🎯 DEBUG AUDIO AI - CONTINUOUS MODE")
        print("="*60)
        print("📢 Speak clearly into microphone")
        print("🔊 For system audio: play speakers near microphone")
        print("⏹️  Press Ctrl+C to stop")
        print("="*60)
        
        success_count = 0
        attempt_count = 0
        
        try:
            while self.is_running:
                attempt_count += 1
                print(f"\n🔄 Attempt {attempt_count}")
                
                text = self.listen_once()
                
                if text:
                    success_count += 1
                    print(f"🎤 DETECTED: {text}")
                    
                    # Get AI response
                    ai_response = self.get_ai_response(text)
                    print(f"🤖 AI RESPONSE: {ai_response}")
                    print("─" * 60)
                    print(f"📊 Success rate: {success_count}/{attempt_count}")
                
                time.sleep(0.5)  # Small delay
                
        except KeyboardInterrupt:
            print(f"\n🛑 Stopping... Final stats: {success_count}/{attempt_count} successful")
            self.stop()
    
    def run_single_test(self):
        """Run single test"""
        print("\n🧪 SINGLE TEST MODE")
        print("-" * 30)
        
        text = self.listen_once()
        
        if text:
            ai_response = self.get_ai_response(text)
            print(f"\n🎉 SUCCESS!")
            print(f"🎤 Input: {text}")
            print(f"🤖 Output: {ai_response}")
        else:
            print("\n❌ No speech detected")
            print("💡 Try speaking louder or closer to microphone")
    
    def stop(self):
        """Stop the AI"""
        self.is_running = False
        print("✅ Debug Audio AI stopped")

def main():
    API_KEY = "AIzaSyCJqF9zkb6U2vr5dQ7-JwHhBNp5TRDZjVY"
    
    print("🔧 DEBUG AUDIO AI")
    print("🎯 Detailed Logging & Testing")
    print("=" * 40)
    
    # Create AI
    ai = DebugAudioAI(API_KEY)
    
    # Ask user what they want to do
    print("\nChoose mode:")
    print("1. Single test")
    print("2. Continuous mode")
    
    try:
        choice = input("Enter choice (1 or 2): ").strip()
        
        if choice == "1":
            ai.run_single_test()
        elif choice == "2":
            ai.run_continuous()
        else:
            print("Invalid choice, running single test...")
            ai.run_single_test()
            
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
