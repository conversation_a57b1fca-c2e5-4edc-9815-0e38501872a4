import speech_recognition as sr
import google.generativeai as genai
from datetime import datetime

def test_speech_recognition():
    """Test speech recognition"""
    print("🎤 Testing Speech Recognition...")
    
    recognizer = sr.Recognizer()
    microphone = sr.Microphone()
    
    try:
        with microphone as source:
            print("🔊 Adjusting for noise...")
            recognizer.adjust_for_ambient_noise(source, duration=1)
            
            print("🎧 Say something now (5 seconds)...")
            audio = recognizer.listen(source, timeout=5, phrase_time_limit=5)
            
            print("🔄 Converting to text...")
            text = recognizer.recognize_google(audio, language='en-IN')
            
            print(f"✅ DETECTED: {text}")
            return text
            
    except sr.WaitTimeoutError:
        print("❌ No audio detected")
        return None
    except sr.UnknownValueError:
        print("❌ Could not understand audio")
        return None
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_ai_response(text):
    """Test AI response"""
    print("🤖 Testing AI Response...")
    
    try:
        genai.configure(api_key="AIzaSyCJqF9zkb6U2vr5dQ7-JwHhBNp5TRDZjVY")
        model = genai.GenerativeModel('gemini-1.5-flash')
        
        prompt = f"Someone said: '{text}'\nGive a brief response in English:"
        response = model.generate_content(prompt)
        
        print(f"✅ AI RESPONSE: {response.text}")
        return response.text
        
    except Exception as e:
        print(f"❌ AI Error: {e}")
        return None

def main():
    print("🧪 SIMPLE TEST AI")
    print("=" * 30)
    
    # Test 1: Speech Recognition
    text = test_speech_recognition()
    
    if text:
        # Test 2: AI Response
        ai_response = test_ai_response(text)
        
        if ai_response:
            print("\n🎉 SUCCESS! Both speech recognition and AI are working!")
            
            # Ask if user wants to continue
            print("\n" + "="*50)
            print("🔄 Want to continue testing? (y/n)")
            
            while True:
                try:
                    choice = input("Enter choice: ").lower()
                    if choice == 'y':
                        print("\n🎧 Speak again...")
                        text = test_speech_recognition()
                        if text:
                            ai_response = test_ai_response(text)
                    elif choice == 'n':
                        break
                    else:
                        print("Please enter 'y' or 'n'")
                except KeyboardInterrupt:
                    break
        else:
            print("❌ AI test failed")
    else:
        print("❌ Speech recognition test failed")
        print("\n💡 Tips:")
        print("- Speak louder")
        print("- Check microphone permissions")
        print("- Try again")
    
    print("\n✅ Test completed")

if __name__ == "__main__":
    main()
