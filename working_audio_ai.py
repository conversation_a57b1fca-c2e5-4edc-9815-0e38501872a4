import speech_recognition as sr
import google.generativeai as genai
import time
from datetime import datetime

class WorkingAudioAI:
    def __init__(self, api_key):
        print("🚀 Starting Working Audio AI...")
        
        # Configure Gemini AI
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        print("✅ AI configured")
        
        # Initialize speech recognition
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        
        # Test microphone
        print("🎤 Testing microphone...")
        try:
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
            print("✅ Microphone working")
        except Exception as e:
            print(f"❌ Microphone error: {e}")
            return
        
        # Test AI
        print("🤖 Testing AI...")
        try:
            test_response = self.model.generate_content("Say 'AI is working'")
            print(f"✅ AI test: {test_response.text}")
        except Exception as e:
            print(f"❌ AI error: {e}")
            return
        
        self.is_running = False
        print("🎯 Setup complete!")
    
    def listen_for_audio(self):
        """Listen for audio and return text"""
        try:
            print("🎧 Listening...")
            with self.microphone as source:
                # Listen for audio with 3 second timeout
                audio = self.recognizer.listen(source, timeout=3, phrase_time_limit=5)
            
            print("🔄 Converting to text...")
            # Try English first
            try:
                text = self.recognizer.recognize_google(audio, language='en-IN')
                return text
            except:
                try:
                    text = self.recognizer.recognize_google(audio, language='en-US')
                    return text
                except:
                    text = self.recognizer.recognize_google(audio, language='hi-IN')
                    return text
                    
        except sr.WaitTimeoutError:
            return None
        except sr.UnknownValueError:
            return None
        except Exception as e:
            print(f"❌ Audio error: {e}")
            return None
    
    def get_ai_response(self, text):
        """Get AI response"""
        try:
            prompt = f"Someone said: '{text}'\nGive a brief helpful response in English:"
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            print(f"❌ AI response error: {e}")
            return "Sorry, AI error occurred"
    
    def start(self):
        """Start the audio AI"""
        self.is_running = True
        
        print("\n" + "="*50)
        print("🎯 WORKING AUDIO AI STARTED!")
        print("="*50)
        print("📢 Speak into microphone")
        print("🔊 Make sure speakers are playing if you want system audio")
        print("⏹️  Press Ctrl+C to stop")
        print("="*50)
        
        try:
            while self.is_running:
                # Listen for audio
                text = self.listen_for_audio()
                
                if text:
                    print(f"\n🎤 [{datetime.now().strftime('%H:%M:%S')}] DETECTED: {text}")
                    
                    # Get AI response
                    ai_response = self.get_ai_response(text)
                    print(f"🤖 [AI RESPONSE]: {ai_response}")
                    print("-" * 50)
                else:
                    print(".", end="", flush=True)
                
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n🛑 Stopping...")
            self.stop()
    
    def stop(self):
        """Stop the audio AI"""
        self.is_running = False
        print("✅ Audio AI stopped")

def main():
    API_KEY = "AIzaSyCJqF9zkb6U2vr5dQ7-JwHhBNp5TRDZjVY"
    
    print("🎯 WORKING AUDIO AI")
    print("🔧 Simple & Reliable")
    print("-" * 30)
    
    # Create and start AI
    ai = WorkingAudioAI(API_KEY)
    ai.start()

if __name__ == "__main__":
    main()
